# Flutter PATH Fix Script
Write-Host "Fixing Flutter PATH environment..."

# Get current PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

# Clean PATH entries - remove corrupted entries
$cleanEntries = @()
$pathEntries = $currentPath -split ";"

foreach ($entry in $pathEntries) {
    # Skip corrupted entries
    if ($entry -notlike "*search-ms*" -and $entry -notlike "*crumb*" -and $entry -notlike "*displayname*" -and $entry.Trim() -ne "") {
        $cleanEntries += $entry.Trim()
    }
}

# Essential Flutter paths
$flutterPaths = @(
    "C:\src\flutter\bin",
    "C:\Program Files\Git\cmd",
    "C:\Program Files\Git\bin"
)

# Add Flutter paths if not already present
foreach ($path in $flutterPaths) {
    if ($cleanEntries -notcontains $path -and (Test-Path $path)) {
        $cleanEntries += $path
        Write-Host "Added: $path"
    }
}

# Create new clean PATH
$newPath = $cleanEntries -join ";"

# Set the new PATH
[Environment]::SetEnvironmentVariable("PATH", $newPath, "User")

Write-Host "PATH cleaned and Flutter paths added!"
Write-Host "Please restart your terminal for changes to take effect."

# Test Flutter
Write-Host "Testing Flutter..."
$env:PATH = $newPath
flutter --version
